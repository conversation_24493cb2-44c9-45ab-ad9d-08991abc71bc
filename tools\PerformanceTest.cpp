#include "../Server/ControlWindow.h"
#include "../common/PerformanceConfig.h"
#include <Windows.h>
#include <iostream>
#include <chrono>

using namespace std;
using namespace std::chrono;
using namespace PerformanceConfig;

// Simple performance test for the optimizations
class PerformanceTest {
private:
    high_resolution_clock::time_point startTime;
    string testName;

public:
    PerformanceTest(const string& name) : testName(name) {
        cout << "Starting test: " << testName << "..." << endl;
        startTime = high_resolution_clock::now();
    }

    ~PerformanceTest() {
        auto endTime = high_resolution_clock::now();
        auto duration = duration_cast<milliseconds>(endTime - startTime);
        cout << "Test '" << testName << "' completed in " << duration.count() << "ms" << endl;
    }
};

void TestWindowCreation() {
    PerformanceTest test("Window Creation");
    
    // Test window class registration
    WNDPROC dummyProc = [](HWND hwnd, UINT msg, W<PERSON><PERSON><PERSON> wParam, LPARAM lParam) -> LRESULT {
        return DefWindowProc(hwnd, msg, wParam, lParam);
    };
    
    if (!CW_Register(dummyProc)) {
        cout << "ERROR: Failed to register window class" << endl;
        return;
    }
    
    // Test window creation with optimizations
    HWND hWnd = CW_Create(0x12345678, 800, 600);
    if (!hWnd) {
        cout << "ERROR: Failed to create optimized window" << endl;
        return;
    }
    
    cout << "Window created successfully with optimizations" << endl;
    
    // Test performance settings
    PerformanceSettings settings;
    CW_GetPerformanceSettings(&settings);
    cout << "Current JPEG quality: " << settings.imageQuality << endl;
    cout << "Current frame rate limit: " << settings.frameRateLimit << endl;
    
    DestroyWindow(hWnd);
}

void TestPerformanceSettings() {
    PerformanceTest test("Performance Settings");
    
    // Test quality settings
    CW_SetImageQuality(QUALITY_HIGH);
    CW_SetFrameRate(60);
    
    PerformanceSettings settings;
    CW_GetPerformanceSettings(&settings);
    
    cout << "Quality set to: " << settings.imageQuality << endl;
    cout << "Frame rate set to: " << settings.frameRateLimit << endl;
    
    // Test frame skipping logic
    int skippedFrames = 0;
    int totalFrames = 100;
    
    for (int i = 0; i < totalFrames; i++) {
        if (CW_ShouldSkipFrame()) {
            skippedFrames++;
        }
        Sleep(1); // Simulate frame processing time
    }
    
    cout << "Frame skip test: " << skippedFrames << "/" << totalFrames << " frames skipped" << endl;
}

void TestConfigurationSystem() {
    PerformanceTest test("Configuration System");
    
    // Test loading default configuration
    PerformanceProfile profile = PERFORMANCE_PROFILES[1]; // Office preset
    
    cout << "Testing configuration save/load..." << endl;
    
    // Save test configuration
    if (SaveConfigToFile("test_config.ini", &profile)) {
        cout << "Configuration saved successfully" << endl;
    } else {
        cout << "ERROR: Failed to save configuration" << endl;
        return;
    }
    
    // Load test configuration
    PerformanceProfile loadedProfile;
    if (LoadConfigFromFile("test_config.ini", &loadedProfile)) {
        cout << "Configuration loaded successfully" << endl;
        cout << "Profile name: " << loadedProfile.profileName << endl;
        cout << "JPEG quality: " << loadedProfile.quality.jpegQuality << endl;
    } else {
        cout << "ERROR: Failed to load configuration" << endl;
    }
    
    // Test system optimization
    OptimizeForCurrentSystem(&profile);
    cout << "System optimization completed" << endl;
    cout << "Optimized JPEG quality: " << profile.quality.jpegQuality << endl;
    cout << "Optimized frame rate: " << profile.quality.frameRateLimit << endl;
    
    // Clean up test file
    DeleteFileA("test_config.ini");
}

void TestSystemDetection() {
    PerformanceTest test("System Detection");
    
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    cout << "CPU cores detected: " << sysInfo.dwNumberOfProcessors << endl;
    
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    GlobalMemoryStatusEx(&memStatus);
    cout << "Total RAM: " << (memStatus.ullTotalPhys / (1024 * 1024 * 1024)) << " GB" << endl;
    
    // Test Windows version detection
    OSVERSIONINFOEX osvi = { 0 };
    osvi.dwOSVersionInfoSize = sizeof(osvi);
    
    typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
    HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
    if (hMod) {
        RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
        if (RtlGetVersion) {
            RtlGetVersion((PRTL_OSVERSIONINFOW)&osvi);
            cout << "Windows version: " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion;
            cout << " (Build " << osvi.dwBuildNumber << ")" << endl;
            
            if (osvi.dwMajorVersion >= 10 && osvi.dwBuildNumber >= 22000) {
                cout << "Windows 11 optimizations available" << endl;
            } else if (osvi.dwMajorVersion >= 10) {
                cout << "Windows 10 optimizations available" << endl;
            }
        }
    }
    
    // Test DWM availability
    HMODULE hDwmapi = LoadLibraryW(L"dwmapi.dll");
    if (hDwmapi) {
        cout << "DWM optimizations available" << endl;
        FreeLibrary(hDwmapi);
    } else {
        cout << "DWM not available" << endl;
    }
}

int main() {
    cout << "HVNC Performance Test Suite" << endl;
    cout << "===========================" << endl << endl;
    
    try {
        TestSystemDetection();
        cout << endl;
        
        TestConfigurationSystem();
        cout << endl;
        
        TestPerformanceSettings();
        cout << endl;
        
        TestWindowCreation();
        cout << endl;
        
        cout << "All tests completed successfully!" << endl;
        cout << "The performance optimizations are working correctly." << endl;
        
    } catch (const exception& e) {
        cout << "Test failed with exception: " << e.what() << endl;
        return 1;
    }
    
    cout << "\nPress Enter to exit...";
    cin.get();
    return 0;
}
