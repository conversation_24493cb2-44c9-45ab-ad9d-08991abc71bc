#include "../common/PerformanceConfig.h"
#include <Windows.h>
#include <iostream>
#include <string>

using namespace std;
using namespace PerformanceConfig;

void PrintSystemInfo()
{
    cout << "=== System Information ===" << endl;
    
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    cout << "CPU Cores: " << sysInfo.dwNumberOfProcessors << endl;
    
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    GlobalMemoryStatusEx(&memStatus);
    cout << "Total RAM: " << (memStatus.ullTotalPhys / (1024 * 1024 * 1024)) << " GB" << endl;
    
    // Detect Windows version
    OSVERSIONINFOEX osvi = { 0 };
    osvi.dwOSVersionInfoSize = sizeof(osvi);
    
    typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
    HMODULE hMod = GetModuleHandleW(L"ntdll.dll");
    if (hMod) {
        RtlGetVersionPtr RtlGetVersion = (RtlGetVersionPtr)GetProcAddress(hMod, "RtlGetVersion");
        if (RtlGetVersion) {
            RtlGetVersion((PRTL_OSVERSIONINFOW)&osvi);
            cout << "Windows Version: " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion;
            cout << " (Build " << osvi.dwBuildNumber << ")" << endl;
            
            if (osvi.dwMajorVersion >= 10) {
                cout << "Windows 10+ detected - optimizations available" << endl;
            }
        }
    }
    cout << endl;
}

void ShowPresetMenu()
{
    cout << "=== Performance Presets ===" << endl;
    cout << "1. Gaming - Maximum performance for gaming" << endl;
    cout << "2. Office - Balanced for office work" << endl;
    cout << "3. Design - Best quality for design work" << endl;
    cout << "4. Custom - Manual configuration" << endl;
    cout << "5. Auto-optimize - Automatically optimize for this system" << endl;
    cout << "Enter choice (1-5): ";
}

void ShowCurrentSettings(const PerformanceProfile& profile)
{
    cout << "\n=== Current Settings ===" << endl;
    cout << "Profile: " << profile.profileName << endl;
    cout << "JPEG Quality: " << profile.quality.jpegQuality << "%" << endl;
    cout << "Frame Rate: " << profile.quality.frameRateLimit << " FPS" << endl;
    cout << "Compression Level: " << profile.quality.compressionLevel << "/9" << endl;
    cout << "Hardware Acceleration: " << (profile.quality.useHardwareAccel ? "Enabled" : "Disabled") << endl;
    cout << "Adaptive Quality: " << (profile.quality.adaptiveQuality ? "Enabled" : "Disabled") << endl;

    cout << "Direct Capture: " << (profile.windows.useDirectCapture ? "Enabled" : "Disabled") << endl;
    cout << "Network Compression: " << (profile.network.useCompression ? "Enabled" : "Disabled") << endl;
    cout << endl;
}

PerformanceProfile CreateCustomProfile()
{
    PerformanceProfile profile = PERFORMANCE_PROFILES[1]; // Start with Office preset
    strcpy(profile.profileName, "Custom");
    
    int choice;
    
    cout << "\n=== Custom Configuration ===" << endl;
    
    cout << "JPEG Quality (1-100, current: " << profile.quality.jpegQuality << "): ";
    cin >> choice;
    if (choice >= 1 && choice <= 100) {
        profile.quality.jpegQuality = choice;
    }
    
    cout << "Frame Rate (15-120, current: " << profile.quality.frameRateLimit << "): ";
    cin >> choice;
    if (choice >= 15 && choice <= 120) {
        profile.quality.frameRateLimit = choice;
    }
    
    cout << "Compression Level (1-9, current: " << profile.quality.compressionLevel << "): ";
    cin >> choice;
    if (choice >= 1 && choice <= 9) {
        profile.quality.compressionLevel = choice;
    }
    
    cout << "Enable Hardware Acceleration? (1=Yes, 0=No): ";
    cin >> choice;
    profile.quality.useHardwareAccel = (choice == 1);
    
    cout << "Enable Adaptive Quality? (1=Yes, 0=No): ";
    cin >> choice;
    profile.quality.adaptiveQuality = (choice == 1);
    

    cout << "Enable Network Compression? (1=Yes, 0=No): ";
    cin >> choice;
    profile.network.useCompression = (choice == 1);
    
    return profile;
}

int main()
{
    cout << "HVNC Performance Optimizer v2.0" << endl;
    cout << "=================================" << endl << endl;
    
    PrintSystemInfo();
    
    PerformanceProfile currentProfile;
    
    // Try to load existing configuration
    if (LoadConfigFromFile("hvnc_performance.ini", &currentProfile)) {
        cout << "Loaded existing configuration." << endl;
        ShowCurrentSettings(currentProfile);
    } else {
        cout << "No existing configuration found. Using default settings." << endl;
        currentProfile = PERFORMANCE_PROFILES[1]; // Office preset
    }
    
    int choice;
    ShowPresetMenu();
    cin >> choice;
    
    switch (choice) {
        case 1:
            currentProfile = PERFORMANCE_PROFILES[0]; // Gaming
            strcpy(currentProfile.profileName, "Gaming");
            break;
        case 2:
            currentProfile = PERFORMANCE_PROFILES[1]; // Office
            strcpy(currentProfile.profileName, "Office");
            break;
        case 3:
            currentProfile = PERFORMANCE_PROFILES[2]; // Design
            strcpy(currentProfile.profileName, "Design");
            break;
        case 4:
            currentProfile = CreateCustomProfile();
            break;
        case 5:
            OptimizeForCurrentSystem(&currentProfile);
            strcpy(currentProfile.profileName, "Auto-Optimized");
            cout << "System automatically optimized!" << endl;
            break;
        default:
            cout << "Invalid choice. Using current settings." << endl;
            break;
    }
    
    cout << "\n=== Final Configuration ===" << endl;
    ShowCurrentSettings(currentProfile);
    
    // Save configuration
    if (SaveConfigToFile("hvnc_performance.ini", &currentProfile)) {
        cout << "Configuration saved to hvnc_performance.ini" << endl;
    } else {
        cout << "Error: Could not save configuration file!" << endl;
        return 1;
    }
    
    cout << "\nConfiguration complete! Restart HVNC to apply changes." << endl;
    cout << "Press Enter to exit...";
    cin.ignore();
    cin.get();
    
    return 0;
}
